-- EMERGENCY TRANSLATION RECOVERY SCRIPT
-- This script restores critical Portuguese translations that were lost due to database seed command

-- First, let's add back the essential Portuguese translations we can see from the backup
INSERT IGNORE INTO translations (status, locale, `group`, `key`, value, created_at, updated_at) VALUES
-- Essential order and delivery translations
(1, 'pt-BR', 'web', 'min.delivery.price', 'Pedido mínimo', NOW(), NOW()),
(1, 'pt-BR', 'web', 'min.price', 'Valor pedido mínimo', NOW(), NOW()),
(1, 'pt-BR', 'web', 'spent.since.registration', 'Total gasto', NOW(), NOW()),
(1, 'pt-BR', 'web', 'orders.count', 'Total de pedidos', NOW(), NOW()),
(1, 'pt-BR', 'web', 'parcel.types', 'Tipos de encomenda', NOW(), NOW()),
(1, 'pt-BR', 'web', 'add.option', 'Adicionar opção', NOW(), NOW()),
(1, 'pt-BR', 'web', 'add.parcel.order', 'Adicionar Pedido de Encomenda', NOW(), NOW()),
(1, 'pt-BR', 'web', 'parcel.reviews', 'Avaliações de Encomendas', NOW(), NOW()),
(1, 'pt-BR', 'web', 'deliveryman.zone', 'Zona do entregador', NOW(), NOW()),
(1, 'pt-BR', 'web', 'place.selected.map', 'Local selecionado no mapa', NOW(), NOW()),
(1, 'pt-BR', 'web', 'delivery.edit', 'Editar entregador', NOW(), NOW()),
(1, 'pt-BR', 'web', 'price', 'Quantia', NOW(), NOW()),
(1, 'pt-BR', 'web', 'add.parcel.type', 'Adicionar Tipo de Encomenda', NOW(), NOW()),
(1, 'pt-BR', 'web', 'last.payment.type', 'Forma de pagamento', NOW(), NOW()),
(1, 'pt-BR', 'web', 'add.payment', 'Adicionar Gateway de Pagamento', NOW(), NOW()),
(1, 'pt-BR', 'web', 'select.deliveryman', 'Selecione o Entregador', NOW(), NOW()),

-- Product and order related translations
(1, 'pt-BR', 'web', 'product.name', 'Produto', NOW(), NOW()),
(1, 'pt-BR', 'web', 'total.amount', 'Total', NOW(), NOW()),
(1, 'pt-BR', 'web', 'total.price', 'Total', NOW(), NOW()),
(1, 'pt-BR', 'web', 'payment.status', 'Situação pag', NOW(), NOW()),
(1, 'pt-BR', 'web', 'number.of.products', 'Qtd. prod.', NOW(), NOW()),

-- Status and activity translations
(1, 'pt-BR', 'web', 'active', 'Ativo', NOW(), NOW()),
(1, 'pt-BR', 'web', 'inactive', 'Inativo', NOW(), NOW()),
(1, 'pt-BR', 'web', 'tip.type', 'Tipo de Gorjeta', NOW(), NOW()),

-- Categories and products
(1, 'pt-BR', 'web', 'top.categories', 'Top categorias', NOW(), NOW()),
(1, 'pt-BR', 'web', 'top.products', 'Top produtos', NOW(), NOW()),
(1, 'pt-BR', 'web', 'order.products', 'Produtos do pedido', NOW(), NOW()),

-- Our new dot-notation translation keys that were working
(1, 'pt-BR', 'web', 'supplier.not.assigned.or.pickup.delivery', 'Entregador não atribuído ou tipo de entrega retirada', NOW(), NOW()),
(1, 'pt-BR', 'web', 'please.make.order.status.ready', 'Por favor, defina o status do pedido como pronto e entre em contato com o entregador', NOW(), NOW()),

-- Essential POS system translations
(1, 'pt-BR', 'web', 'cash', 'Dinheiro', NOW(), NOW()),
(1, 'pt-BR', 'web', 'card', 'Cartão', NOW(), NOW()),
(1, 'pt-BR', 'web', 'debit', 'Débito', NOW(), NOW()),
(1, 'pt-BR', 'web', 'pix', 'PIX', NOW(), NOW()),
(1, 'pt-BR', 'web', 'cash_delivery', 'Dinheiro na Entrega', NOW(), NOW()),
(1, 'pt-BR', 'web', 'card_delivery', 'Cartão na Entrega', NOW(), NOW()),
(1, 'pt-BR', 'web', 'debit_delivery', 'Débito na Entrega', NOW(), NOW()),
(1, 'pt-BR', 'web', 'pix_delivery', 'PIX na Entrega', NOW(), NOW()),

-- Common UI translations
(1, 'pt-BR', 'web', 'save', 'Salvar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'cancel', 'Cancelar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'edit', 'Editar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'delete', 'Excluir', NOW(), NOW()),
(1, 'pt-BR', 'web', 'add', 'Adicionar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'update', 'Atualizar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'create', 'Criar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'view', 'Visualizar', NOW(), NOW()),
(1, 'pt-BR', 'web', 'details', 'Detalhes', NOW(), NOW()),
(1, 'pt-BR', 'web', 'status', 'Status', NOW(), NOW()),
(1, 'pt-BR', 'web', 'name', 'Nome', NOW(), NOW()),
(1, 'pt-BR', 'web', 'description', 'Descrição', NOW(), NOW()),
(1, 'pt-BR', 'web', 'address', 'Endereço', NOW(), NOW()),
(1, 'pt-BR', 'web', 'phone', 'Telefone', NOW(), NOW()),
(1, 'pt-BR', 'web', 'email', 'E-mail', NOW(), NOW()),
(1, 'pt-BR', 'web', 'date', 'Data', NOW(), NOW()),
(1, 'pt-BR', 'web', 'time', 'Hora', NOW(), NOW()),

-- Order status translations
(1, 'pt-BR', 'web', 'new', 'Novo', NOW(), NOW()),
(1, 'pt-BR', 'web', 'accepted', 'Aceito', NOW(), NOW()),
(1, 'pt-BR', 'web', 'ready', 'Pronto', NOW(), NOW()),
(1, 'pt-BR', 'web', 'on_a_way', 'A caminho', NOW(), NOW()),
(1, 'pt-BR', 'web', 'delivered', 'Entregue', NOW(), NOW()),
(1, 'pt-BR', 'web', 'canceled', 'Cancelado', NOW(), NOW()),

-- Delivery types
(1, 'pt-BR', 'web', 'delivery', 'Entrega', NOW(), NOW()),
(1, 'pt-BR', 'web', 'pickup', 'Retirada', NOW(), NOW()),
(1, 'pt-BR', 'web', 'dine_in', 'Comer no Local', NOW(), NOW());

-- Verify the recovery
SELECT 
    'RECOVERY SUMMARY' as info,
    COUNT(*) as total_pt_br_translations
FROM translations 
WHERE locale = 'pt-BR';

-- Show sample of recovered translations
SELECT 
    'SAMPLE RECOVERED TRANSLATIONS' as info,
    `key`,
    value
FROM translations 
WHERE locale = 'pt-BR' 
ORDER BY `key`
LIMIT 10;
