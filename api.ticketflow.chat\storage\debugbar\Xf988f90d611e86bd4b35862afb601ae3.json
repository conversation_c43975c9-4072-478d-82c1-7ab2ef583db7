{"__meta": {"id": "Xf988f90d611e86bd4b35862afb601ae3", "datetime": "2025-07-26 20:50:14", "utime": 1753573814.01651, "method": "GET", "uri": "/api/v1/rest/shops/501/products?lang=pt-BR&currency_id=1", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[20:50:13] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753573813.83853, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753573813.551382, "end": 1753573814.016529, "duration": 0.4651470184326172, "duration_str": "465ms", "measures": [{"label": "Booting", "start": 1753573813.551382, "relative_start": 0, "end": 1753573813.822446, "relative_end": 1753573813.822446, "duration": 0.27106404304504395, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753573813.822454, "relative_start": 0.2710719108581543, "end": 1753573814.016532, "relative_end": 2.86102294921875e-06, "duration": 0.1940779685974121, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43694632, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/rest/shops/{id}/products", "middleware": "api, block.ip", "controller": "App\\Http\\Controllers\\API\\v1\\Rest\\ShopController@products", "namespace": null, "prefix": "api/v1/rest", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php&line=252\">\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:252-266</a>"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.04223000000000001, "accumulated_duration_str": "42.23ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.02502, "duration_str": "25.02ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 59.247}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 59.247, "width_percent": 0.924}, {"sql": "select count(*) as aggregate from `currencies` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 60.17, "width_percent": 1.184}, {"sql": "select count(*) as aggregate from `languages` where `locale` = 'pt-BR'", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 61.354, "width_percent": 0.687}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 254}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:254", "connection": "foodyman", "start_percent": 62.041, "width_percent": 1.563}, {"sql": "select * from `categories` where `categories`.`id` is null and `categories`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 747}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:747", "connection": "foodyman", "start_percent": 63.604, "width_percent": 0.924}, {"sql": "select * from `settings` where `key` = 'recommended_count' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["recommended_count"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 749}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:749", "connection": "foodyman", "start_percent": 64.528, "width_percent": 0.876}, {"sql": "select `id`, `uuid`, `category_id`, `shop_id`, `img`, `status`, `active`, `addon`, `min_qty`, `max_qty`, `interval` from `products` where `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 501 and exists (select * from `categories` where `products`.`category_id` = `categories`.`id` and `categories`.`deleted_at` is null) and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `addon` = 0 and `quantity` > 0 and exists (select * from `order_details` where `stocks`.`id` = `order_details`.`stock_id` and exists (select * from `orders` where `order_details`.`order_id` = `orders`.`id` and `status` = 'delivered')) and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "0", "published", "501", "0", "0", "delivered", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 805}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:805", "connection": "foodyman", "start_percent": 65.404, "width_percent": 2.25}, {"sql": "select `id`, `countable_type`, `countable_id`, `price`, `quantity`, `addon` from `stocks` where `stocks`.`countable_id` in (1, 2) and `addon` = 0 and `quantity` > 0 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 805}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:805", "connection": "foodyman", "start_percent": 67.653, "width_percent": 1.231}, {"sql": "select `id`, `expired_at`, `bonusable_type`, `bonusable_id`, `bonus_quantity`, `value`, `type`, `status` from `bonuses` where `bonuses`.`bonusable_id` in (6, 7) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-26 20:50:13' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-26 20:50:13", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 805}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:805", "connection": "foodyman", "start_percent": 68.885, "width_percent": 1.066}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (1, 2) and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 805}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:805", "connection": "foodyman", "start_percent": 69.95, "width_percent": 0.924}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (1, 2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 805}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:805", "connection": "foodyman", "start_percent": 70.874, "width_percent": 1.089}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'categories'", "type": "query", "params": [], "bindings": ["foodyman", "categories"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 809}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:809", "connection": "foodyman", "start_percent": 71.963, "width_percent": 2.605}, {"sql": "select `id`, `uuid`, `img`, `active`, `status`, `input`, `type`, `shop_id` from `categories` where (`active` = 1 and `status` = 'published') and `type` = 1 and (exists (select * from `products` where `categories`.`id` = `products`.`category_id` and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 501 and `products`.`deleted_at` is null) or exists (select * from `categories` as `laravel_reserved_0` where `categories`.`id` = `laravel_reserved_0`.`parent_id` and exists (select * from `products` where `laravel_reserved_0`.`id` = `products`.`category_id` and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 501 and `products`.`deleted_at` is null) and `laravel_reserved_0`.`deleted_at` is null)) and exists (select * from `category_translations` where `categories`.`id` = `category_translations`.`category_id` and `locale` = 'pt-BR' and `category_translations`.`deleted_at` is null) and `categories`.`deleted_at` is null order by `input` asc", "type": "query", "params": [], "bindings": ["1", "published", "1", "0", "0", "pt-BR", "1", "0", "published", "501", "0", "0", "pt-BR", "1", "0", "published", "501", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00172, "duration_str": "1.72ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 74.568, "width_percent": 4.073}, {"sql": "select `id`, `locale`, `title`, `category_id` from `category_translations` where `category_translations`.`category_id` in (11) and `locale` = 'pt-BR' and `category_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 78.641, "width_percent": 0.829}, {"sql": "select * from `categories` where `categories`.`parent_id` in (11) and `categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 79.47, "width_percent": 1.681}, {"sql": "select `id`, `uuid`, `shop_id`, `brand_id`, `category_id`, `img`, `status`, `active`, `min_qty`, `max_qty`, `interval`, `addon` from `products` where `products`.`category_id` in (11) and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 501 and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0", "pt-BR", "1", "0", "published", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 81.151, "width_percent": 2.936}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (1, 2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 84.087, "width_percent": 1.634}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (1, 2) and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 85.721, "width_percent": 1.042}, {"sql": "select `id`, `countable_type`, `countable_id`, `price`, `quantity`, `addon` from `stocks` where `stocks`.`countable_id` in (1, 2) and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 86.763, "width_percent": 1.231}, {"sql": "select `id`, `expired_at`, `bonusable_type`, `bonusable_id`, `bonus_quantity`, `value`, `type`, `status` from `bonuses` where `bonuses`.`bonusable_id` in (6, 7) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-26 20:50:13' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-26 20:50:13", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 29, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 30, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 264}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 87.994, "width_percent": 2.51}, {"sql": "select * from `products` where `products`.`id` = 1 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 90.504, "width_percent": 1.468}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 91.973, "width_percent": 1.137}, {"sql": "select * from `products` where `products`.`id` = 2 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 93.109, "width_percent": 1.421}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 2 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 94.53, "width_percent": 1.089}, {"sql": "select * from `products` where `products`.`id` = 1 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 95.619, "width_percent": 1.208}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 96.827, "width_percent": 0.9}, {"sql": "select * from `products` where `products`.`id` = 2 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 97.727, "width_percent": 1.255}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 2 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 98.982, "width_percent": 1.018}]}, "models": {"data": {"App\\Models\\CategoryTranslation": 1, "App\\Models\\Category": 1, "App\\Models\\ProductTranslation": 4, "App\\Models\\Stock": 4, "App\\Models\\Product": 8, "App\\Models\\Settings": 1, "App\\Models\\Shop": 1, "App\\Models\\Currency": 1, "App\\Models\\Language": 1}, "count": 22}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7c926d-6151-4da8-8eb9-9aae4c742846\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/rest/shops/501/products", "status_code": "<pre class=sf-dump id=sf-dump-1600695179 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1600695179\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1051938113 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051938113\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1948229004 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948229004\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1305126170 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305126170\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-641657735 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61522</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/api/v1/rest/shops/501/products?lang=pt-BR&amp;currency_id=1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/api/v1/rest/shops/501/products</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/index.php/api/v1/rest/shops/501/products</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"24 characters\">lang=pt-BR&amp;currency_id=1</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753573813.5514</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753573813</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">lang=pt-BR&amp;currency_id=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641657735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-526032408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-526032408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-924103537 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 23:50:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4980</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924103537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1059455716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1059455716\", {\"maxDepth\":0})</script>\n"}}