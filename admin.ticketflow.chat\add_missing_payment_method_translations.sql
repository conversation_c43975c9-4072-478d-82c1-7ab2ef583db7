-- Script para adicionar traduções faltantes dos métodos de pagamento
-- Inclui métodos básicos e específicos do Brasil

-- Verificar se as chaves já existem
SET @payment_keys = 'cash,wallet,mercado-pago,stripe,paypal,paystack,razorpay,flutter-wave,mollie,paytabs,zain-cash,iyzico,maksekeskus,pay-fast,moya-sar';

-- Verificar quantas chaves já existem
SELECT 
    COUNT(*) as existing_keys,
    GROUP_CONCAT(DISTINCT t.key SEPARATOR ', ') as existing_key_list
FROM translations t 
WHERE FIND_IN_SET(t.key, @payment_keys) > 0 
AND t.locale = 'pt-BR'
AND t.group = 'web';

-- Inserir traduções em português (pt-BR) apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, status, created_at, updated_at) VALUES
-- Mé<PERSON>dos básicos
('pt-BR', 'web', 'cash', 'Dinheiro', 1, NOW(), NOW()),
('pt-BR', 'web', 'wallet', 'Carteira Digital', 1, NOW(), NOW()),

-- Métodos específicos do Brasil
('pt-BR', 'web', 'mercado-pago', 'Mercado Pago', 1, NOW(), NOW()),
('pt-BR', 'web', 'stripe', 'Cartão de Crédito', 1, NOW(), NOW()),

-- Métodos internacionais
('pt-BR', 'web', 'paypal', 'PayPal', 1, NOW(), NOW()),
('pt-BR', 'web', 'paystack', 'PayStack', 1, NOW(), NOW()),
('pt-BR', 'web', 'razorpay', 'RazorPay', 1, NOW(), NOW()),
('pt-BR', 'web', 'flutter-wave', 'FlutterWave', 1, NOW(), NOW()),
('pt-BR', 'web', 'mollie', 'Mollie', 1, NOW(), NOW()),
('pt-BR', 'web', 'paytabs', 'PayTabs', 1, NOW(), NOW()),
('pt-BR', 'web', 'zain-cash', 'Zain Cash', 1, NOW(), NOW()),
('pt-BR', 'web', 'iyzico', 'Iyzico', 1, NOW(), NOW()),
('pt-BR', 'web', 'maksekeskus', 'Maksekeskus', 1, NOW(), NOW()),
('pt-BR', 'web', 'pay-fast', 'PayFast', 1, NOW(), NOW()),
('pt-BR', 'web', 'moya-sar', 'Moya SAR', 1, NOW(), NOW());

-- Inserir traduções em inglês (en) apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, status, created_at, updated_at) VALUES
-- Métodos básicos
('en', 'web', 'cash', 'Cash', 1, NOW(), NOW()),
('en', 'web', 'wallet', 'Digital Wallet', 1, NOW(), NOW()),

-- Métodos específicos do Brasil
('en', 'web', 'mercado-pago', 'Mercado Pago', 1, NOW(), NOW()),
('en', 'web', 'stripe', 'Credit Card', 1, NOW(), NOW()),

-- Métodos internacionais
('en', 'web', 'paypal', 'PayPal', 1, NOW(), NOW()),
('en', 'web', 'paystack', 'PayStack', 1, NOW(), NOW()),
('en', 'web', 'razorpay', 'RazorPay', 1, NOW(), NOW()),
('en', 'web', 'flutter-wave', 'FlutterWave', 1, NOW(), NOW()),
('en', 'web', 'mollie', 'Mollie', 1, NOW(), NOW()),
('en', 'web', 'paytabs', 'PayTabs', 1, NOW(), NOW()),
('en', 'web', 'zain-cash', 'Zain Cash', 1, NOW(), NOW()),
('en', 'web', 'iyzico', 'Iyzico', 1, NOW(), NOW()),
('en', 'web', 'maksekeskus', 'Maksekeskus', 1, NOW(), NOW()),
('en', 'web', 'pay-fast', 'PayFast', 1, NOW(), NOW()),
('en', 'web', 'moya-sar', 'Moya SAR', 1, NOW(), NOW());

-- Inserir traduções em espanhol (es) apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, status, created_at, updated_at) VALUES
-- Métodos básicos
('es', 'web', 'cash', 'Efectivo', 1, NOW(), NOW()),
('es', 'web', 'wallet', 'Billetera Digital', 1, NOW(), NOW()),

-- Métodos específicos do Brasil
('es', 'web', 'mercado-pago', 'Mercado Pago', 1, NOW(), NOW()),
('es', 'web', 'stripe', 'Tarjeta de Crédito', 1, NOW(), NOW()),

-- Métodos internacionais
('es', 'web', 'paypal', 'PayPal', 1, NOW(), NOW()),
('es', 'web', 'paystack', 'PayStack', 1, NOW(), NOW()),
('es', 'web', 'razorpay', 'RazorPay', 1, NOW(), NOW()),
('es', 'web', 'flutter-wave', 'FlutterWave', 1, NOW(), NOW()),
('es', 'web', 'mollie', 'Mollie', 1, NOW(), NOW()),
('es', 'web', 'paytabs', 'PayTabs', 1, NOW(), NOW()),
('es', 'web', 'zain-cash', 'Zain Cash', 1, NOW(), NOW()),
('es', 'web', 'iyzico', 'Iyzico', 1, NOW(), NOW()),
('es', 'web', 'maksekeskus', 'Maksekeskus', 1, NOW(), NOW()),
('es', 'web', 'pay-fast', 'PayFast', 1, NOW(), NOW()),
('es', 'web', 'moya-sar', 'Moya SAR', 1, NOW(), NOW());

-- Verificar quantas traduções foram inseridas
SELECT 
    COUNT(*) as total_translations_added,
    GROUP_CONCAT(DISTINCT t.key SEPARATOR ', ') as added_keys
FROM translations t 
WHERE FIND_IN_SET(t.key, @payment_keys) > 0 
AND t.locale IN ('pt-BR', 'en', 'es')
AND t.group = 'web';

-- Mostrar todas as traduções de métodos de pagamento
SELECT 
    t.locale,
    t.key,
    t.value
FROM translations t 
WHERE (
    FIND_IN_SET(t.key, @payment_keys) > 0
    OR t.key LIKE '%_delivery'
    OR t.key LIKE 'payment%'
)
AND t.locale IN ('pt-BR', 'en', 'es')
AND t.group = 'web'
ORDER BY t.key, t.locale;
