{"__meta": {"id": "X92ac53dff3a8fdb34a173d26c7828d0a", "datetime": "2025-07-26 22:38:05", "utime": 1753580285.988128, "method": "POST", "uri": "/api/v1/dashboard/user/profile/firebase/token/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[22:38:05] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753580285.907989, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753580285.621862, "end": 1753580285.988149, "duration": 0.3662869930267334, "duration_str": "366ms", "measures": [{"label": "Booting", "start": 1753580285.621862, "relative_start": 0, "end": 1753580285.886643, "relative_end": 1753580285.886643, "duration": 0.26478099822998047, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753580285.886661, "relative_start": 0.2647991180419922, "end": 1753580285.988152, "relative_end": 3.0994415283203125e-06, "duration": 0.10149097442626953, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 41367256, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/user/profile/firebase/token/update", "middleware": "api, block.ip, sanctum.check", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController@fireBaseTokenUpdate", "as": "user.", "namespace": null, "prefix": "api/v1/dashboard/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php&line=139\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:139-164</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01004, "accumulated_duration_str": "10.04ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00383, "duration_str": "3.83ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 38.147}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 38.147, "width_percent": 6.574}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 44.721, "width_percent": 5.478}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 50.199, "width_percent": 4.183}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '27' limit 1", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 54.382, "width_percent": 9.064}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 63.446, "width_percent": 6.574}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-26 22:38:05', `personal_access_tokens`.`updated_at` = '2025-07-26 22:38:05' where `id` = 27", "type": "query", "params": [], "bindings": ["2025-07-26 22:38:05", "2025-07-26 22:38:05", "27"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 70.02, "width_percent": 14.841}, {"sql": "select * from `users` where `uuid` = '2678ed1e-cb47-4f9d-906b-5250af807e01' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2678ed1e-cb47-4f9d-906b-5250af807e01"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 145}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:145", "connection": "foodyman", "start_percent": 84.861, "width_percent": 4.681}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'users'", "type": "query", "params": [], "bindings": ["foodyman", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php", "line": 157}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\ProfileController.php:157", "connection": "foodyman", "start_percent": 89.542, "width_percent": 10.458}]}, "models": {"data": {"App\\Models\\User": 2, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 2, "App\\Models\\Language": 2}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7cb900-d39f-4335-ac65-e61c555b8409\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/user/profile/firebase/token/update", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1848670956 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848670956\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-540605283 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_token</span>\" => \"<span class=sf-dump-str title=\"142 characters\">czShvYoPj2uhSRtRctMkvt:APA91bGzlAzreJ7qhrZdAzmdd5QYineJLoE7SriA7666wnYec5TAdHdYH7L0WaoxNJMbGi-RznCxs2aOwFPVXMnckSGVjoaRvoWxZpd13seCfEH8OPJAXOU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540605283\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1639289825 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 27|tyw1qG5R6as2isoKjKPNvYsnxu5LAw1kUKOlOFq5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639289825\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-211179047 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62887</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/api/v1/dashboard/user/profile/firebase/token/update</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/api/v1/dashboard/user/profile/firebase/token/update</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/index.php/api/v1/dashboard/user/profile/firebase/token/update</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 27|tyw1qG5R6as2isoKjKPNvYsnxu5LAw1kUKOlOFq5</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753580285.6219</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753580285</span>\n  \"<span class=sf-dump-key>argv</span>\" => []\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211179047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1971006712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1971006712\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1706649268 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 01:38:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4979</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706649268\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-289287143 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-289287143\", {\"maxDepth\":0})</script>\n"}}