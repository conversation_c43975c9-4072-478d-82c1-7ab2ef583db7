-- Script para adicionar tradução para "Transaction for order #" 
-- Adiciona chave de tradução seguindo padrão dot-notation do projeto

-- Verificar se a chave já existe
SELECT 
    t.locale,
    t.key,
    t.value
FROM translations t 
WHERE t.key = 'transaction.for.order'
ORDER BY t.locale;

-- Inserir traduções para "Transaction for order #" apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, status, created_at, updated_at) VALUES
('pt-BR', 'web', 'transaction.for.order', 'Transação para pedido #', 1, NOW(), NOW()),
('en', 'web', 'transaction.for.order', 'Transaction for order #', 1, NOW(), NOW()),
('es', 'web', 'transaction.for.order', 'Transacción para pedido #', 1, NOW(), NOW());

-- Verificar se as traduções foram inseridas corretamente
SELECT 
    t.locale,
    t.key,
    t.value,
    t.created_at
FROM translations t 
WHERE t.key = 'transaction.for.order'
ORDER BY t.locale;

-- <PERSON>rar todas as traduções para esta chave
SELECT 
    'NOVA CHAVE ADICIONADA' as tipo,
    t.locale,
    t.key,
    t.value
FROM translations t 
WHERE t.key = 'transaction.for.order'
ORDER BY t.locale;
