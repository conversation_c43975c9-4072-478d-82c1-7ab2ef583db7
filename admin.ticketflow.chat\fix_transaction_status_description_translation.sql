-- Script para adicionar tradução para "Transaction for order #" 
-- Adiciona chave de tradução seguindo padrão dot-notation do projeto

-- Verificar se as chaves já existem
SELECT
    t.locale,
    t.key,
    t.value
FROM translations t
WHERE t.key IN ('transaction.for.order', 'transaction.for.model', 'transaction.for.ads', 'transaction.for.subscription')
ORDER BY t.key, t.locale;

-- Inserir traduções para "Transaction for order #" apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, status, created_at, updated_at) VALUES
('pt-BR', 'web', 'transaction.for.order', 'Transação para pedido #', 1, NOW(), NOW()),
('en', 'web', 'transaction.for.order', 'Transaction for order #', 1, NOW(), NOW()),
('es', 'web', 'transaction.for.order', 'Transacción para pedido #', 1, NOW(), NOW()),

-- Inserir traduções para "Transaction for model #" apenas se não existirem
('pt-BR', 'web', 'transaction.for.model', 'Transação para modelo #', 1, NOW(), NOW()),
('en', 'web', 'transaction.for.model', 'Transaction for model #', 1, NOW(), NOW()),
('es', 'web', 'transaction.for.model', 'Transacción para modelo #', 1, NOW(), NOW()),

-- Inserir traduções para "Transaction for ads #" apenas se não existirem
('pt-BR', 'web', 'transaction.for.ads', 'Transação para anúncio #', 1, NOW(), NOW()),
('en', 'web', 'transaction.for.ads', 'Transaction for ads #', 1, NOW(), NOW()),
('es', 'web', 'transaction.for.ads', 'Transacción para anuncio #', 1, NOW(), NOW()),

-- Inserir traduções para "Transaction for Subscription #" apenas se não existirem
('pt-BR', 'web', 'transaction.for.subscription', 'Transação para assinatura #', 1, NOW(), NOW()),
('en', 'web', 'transaction.for.subscription', 'Transaction for Subscription #', 1, NOW(), NOW()),
('es', 'web', 'transaction.for.subscription', 'Transacción para suscripción #', 1, NOW(), NOW());

-- Verificar se as traduções foram inseridas corretamente
SELECT
    t.locale,
    t.key,
    t.value,
    t.created_at
FROM translations t
WHERE t.key IN ('transaction.for.order', 'transaction.for.model', 'transaction.for.ads', 'transaction.for.subscription')
ORDER BY t.key, t.locale;

-- Mostrar todas as traduções para estas chaves
SELECT
    'NOVAS CHAVES ADICIONADAS' as tipo,
    t.locale,
    t.key,
    t.value
FROM translations t
WHERE t.key IN ('transaction.for.order', 'transaction.for.model', 'transaction.for.ads', 'transaction.for.subscription')
ORDER BY t.key, t.locale;
